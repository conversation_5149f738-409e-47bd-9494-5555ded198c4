<?php

namespace App\Services;

use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;

class OsrmService
{
    /**
     * Base URL for the OSRM service within the Docker network.
     *
     * @var string
     */
    protected $baseUrl = 'http://osrm:5000';

    /**
     * Default options for the OSRM route query.
     * Each option is configured with defaults and can be overridden by passing a params array to the request method.
     *
     * @var array
     */
    protected $options = [
        // Required: Array of coordinates in [longitude, latitude] format, in decimal degrees.
        // Example: [[30.1068336, -2.0240007], [30.0618851, -1.9440727]] for Gahanga to Kigali.
        // Must contain at least two coordinates for a route.
        'coordinates' => [],

        // Optional: Whether to include detailed route steps for each leg (e.g., turn-by-turn instructions).
        // Values: true (include steps), false (exclude steps, default).
        'steps' => false,

        // Optional: Format of the returned route geometry.
        // Values: 'polyline' (encoded polyline string, default), 'geojson' (GeoJSON format).
        'geometries' => 'polyline',

        // Optional: Level of detail for the route overview geometry.
        // Values: 'full' (detailed geometry), 'simplified' (simplified for zoom level, default), 'false' (no overview geometry).
        'overview' => 'simplified',

        // Optional: Whether to include alternative routes in the response.
        // Values: true (include alternatives), false (only primary route, default).
        // Note: Alternatives are not guaranteed even if requested.
        'alternatives' => false,

        // Optional: Include additional data in the response (e.g., duration, distance per leg).
        // Values: false (no annotations, default), true (all annotations), or array of specific annotations
        //         (e.g., ['duration', 'distance', 'nodes', 'weight', 'datasources', 'speed']).
        // Example: ['duration', 'distance'] to include travel time and distance.
        'annotations' => false,

        // Optional: Limit search to road segments with specific bearings (degrees from true north, clockwise).
        // Format: Array of [value, range] pairs, where value is 0-360 (degrees), range is 0-180 (degrees).
        // Example: [[0, 20], [90, 30]] to limit to segments facing ~north or ~east.
        // Default: null (no bearing restrictions).
        'bearings' => null,

        // Optional: Limit coordinate snapping to roads within a given radius (meters).
        // Format: Array of radius values (double >= 0) for each coordinate, or null for unlimited.
        // Example: [50, 50] to limit snapping to 50 meters for each coordinate.
        // Default: null (unlimited radius).
        'radiuses' => null,

        // Optional: Hints for coordinate snapping (base64-encoded strings from previous OSRM responses).
        // Format: Array of hint strings, one per coordinate.
        // Example: ['hint1_base64', 'hint2_base64'].
        // Default: null (no hints).
        'hints' => null,

        // Optional: Road classes to avoid in routing (e.g., 'motorway', 'toll').
        // Format: Array of class names.
        // Example: ['motorway', 'ferry'] to avoid motorways and ferries.
        // Default: null (no exclusions).
        'exclude' => null,

        // Optional: Restrict how coordinates are approached in the road network.
        // Format: Array of approach types for each coordinate: 'curb' (approach from curb side),
        //         'opposite' (approach from opposite side), or null (unrestricted).
        // Example: ['curb', 'opposite'].
        // Default: null (unrestricted approaches).
        'approaches' => null,

        // Optional: Indices of coordinates to treat as waypoints (must include first and last coordinate).
        // Format: Array of integer indices (0-based) from the coordinates array.
        // Example: [0, 2] to treat first and third coordinates as waypoints.
        // Default: null (all coordinates are waypoints).
        'waypoints' => null,
    ];

    /**
     * Make a request to the OSRM route endpoint.
     *
     * @param array $params Options for the route query, overriding defaults in $options.
     * @return array Response from the OSRM API.
     */
    public function request(array $params): array
    {
        // Merge provided params with default options
        $queryParams = array_merge($this->options, $params);

        // Format coordinates as semicolon-separated string
        $coordinates = implode(';', array_map(fn($coord) => "{$coord[0]},{$coord[1]}", $queryParams['coordinates']));
        
        // Convert array-based params to semicolon-separated strings
        if (is_array($queryParams['annotations'])) {
            $queryParams['annotations'] = implode(',', $queryParams['annotations']);
        }
        if (is_array($queryParams['bearings'])) {
            $queryParams['bearings'] = implode(';', array_map(fn($b) => implode(',', $b), $queryParams['bearings']));
        }
        if (is_array($queryParams['radiuses'])) {
            $queryParams['radiuses'] = implode(';', $queryParams['radiuses']);
        }
        if (is_array($queryParams['hints'])) {
            $queryParams['hints'] = implode(';', $queryParams['hints']);
        }
        if (is_array($queryParams['exclude'])) {
            $queryParams['exclude'] = implode(',', $queryParams['exclude']);
        }
        if (is_array($queryParams['approaches'])) {
            $queryParams['approaches'] = implode(';', $queryParams['approaches']);
        }
        if (is_array($queryParams['waypoints'])) {
            $queryParams['waypoints'] = implode(';', $queryParams['waypoints']);
        }

        // Make HTTP request to OSRM route endpoint
        $response = Http::get("http://osrm:5000/route/v1/driving/29.420522,-2.1680256;29.7528767,-2.0845106?overview=false&alternatives=true&steps=true",);

        Log::info('OSRM Response: ' . json_encode($response->json()));
        return $response->json();
    }
    
}