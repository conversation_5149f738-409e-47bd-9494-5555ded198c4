<script setup>

import { ref, onMounted, onUnmounted, computed } from 'vue';
import maplibregl from 'maplibre-gl';
import 'maplibre-gl/dist/maplibre-gl.css';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import axios from 'axios';
import { debounce } from 'lodash';
import AppLayout from '@/Layouts/AppLayout.vue';

const props = defineProps({
    provinces: {
        type: Array,
        required: true,
        default: () => [],
    },
    districts: {
        type: Array,
        required: true,
        default: () => [],
    },
});

// Map and UI state
const mapContainer = ref(null);
const map = ref(null);
const panelsVisible = ref(true);

// Map selection state
const mapSelectionMode = ref(null); // 'from', 'to', or throughIndex
const hoverCoordinates = ref(null);
const hoverLocationName = ref('');
const isLoadingHoverLocation = ref(false);

// Navigation form state
const navigationForm = ref({
    from: {
        latitude: null,
        longitude: null,
        name: '',
        searchQuery: ''
    },
    to: {
        latitude: null,
        longitude: null,
        name: '',
        searchQuery: ''
    },
    through: [],
    mode: 'car',
    language: 'en'
});

// Search state
const searchResults = ref({
    from: [],
    to: [],
    through: {}
});
const isSearching = ref({
    from: false,
    to: false,
    through: {}
});

// Map markers
const markers = ref({
    from: null,
    to: null,
    through: []
});

// Map configuration
const MAP_CONFIG = {
    center: [29.8739, -1.9403], // Rwanda center
    zoom: 8,
    minZoom: 6,
    maxZoom: 18,
    bounds: [
        [28.8617, -2.9389], // Southwest coordinates
        [30.8997, -1.0474]  // Northeast coordinates
    ]
};

// Computed properties
const canSubmit = computed(() => {
    return navigationForm.value.from.latitude &&
           navigationForm.value.from.longitude &&
           navigationForm.value.to.latitude &&
           navigationForm.value.to.longitude;
});

const throughPointsOrdered = computed(() => {
    return [...navigationForm.value.through].sort((a, b) => a.index - b.index);
});

// Search functions
const performLocationSearch = debounce(async (type, query, throughIndex = null) => {
    if (query.length < 3) {
        if (throughIndex !== null) {
            searchResults.value.through[throughIndex] = [];
        } else {
            searchResults.value[type] = [];
        }
        return;
    }

    if (throughIndex !== null) {
        isSearching.value.through[throughIndex] = true;
    } else {
        isSearching.value[type] = true;
    }

    try {
        const response = await axios.post('/map/search-json', {
            searchQuery: query.trim(),
            lang: navigationForm.value.language,
            filterData: 'all'
        });

        const allResults = [];
        ['provinces', 'districts', 'sectors', 'cells', 'villages', 'healthFacs'].forEach(resultType => {
            if (response.data[resultType]) {
                response.data[resultType].forEach(item => {
                    allResults.push({
                        ...item,
                        latitude: typeof item.latitude === 'string' ? parseFloat(item.latitude) : item.latitude,
                        longitude: typeof item.longitude === 'string' ? parseFloat(item.longitude) : item.longitude,
                        type: resultType
                    });
                });
            }
        });

        if (throughIndex !== null) {
            searchResults.value.through[throughIndex] = allResults;
        } else {
            searchResults.value[type] = allResults;
        }
    } catch (error) {
        console.error('Search error:', error);
        if (throughIndex !== null) {
            searchResults.value.through[throughIndex] = [];
        } else {
            searchResults.value[type] = [];
        }
    } finally {
        if (throughIndex !== null) {
            isSearching.value.through[throughIndex] = false;
        } else {
            isSearching.value[type] = false;
        }
    }
}, 300);

// Map selection functions
const enableMapSelection = (type, throughIndex = null) => {
    mapSelectionMode.value = throughIndex !== null ? throughIndex : type;
    map.value.getCanvas().style.cursor = 'crosshair';

    // Show instruction message
    const instruction = throughIndex !== null
        ? `Click on the map to select stop ${throughIndex + 1} location`
        : `Click on the map to select ${type} location`;
    console.log(instruction);
};

const disableMapSelection = () => {
    mapSelectionMode.value = null;
    map.value.getCanvas().style.cursor = '';
    hoverCoordinates.value = null;
    hoverLocationName.value = '';
};

const queryLocationName = async (lat, lng) => {
    try {
        isLoadingHoverLocation.value = true;
        const response = await axios.post('/map/search-latitude-langitude-json', {
            latitude: lat,
            longitude: lng,
            lang: navigationForm.value.language
        });

        if (response.data && response.data.length > 0) {
            const location = response.data[0];
            return location.name_en || location.name || `${lat.toFixed(6)}, ${lng.toFixed(6)}`;
        }
        return `${lat.toFixed(6)}, ${lng.toFixed(6)}`;
    } catch (error) {
        console.error('Error querying location name:', error);
        return `${lat.toFixed(6)}, ${lng.toFixed(6)}`;
    } finally {
        isLoadingHoverLocation.value = false;
    }
};

// Location selection functions
const selectLocation = (type, location, throughIndex = null) => {
    if (throughIndex !== null) {
        // Handle through point selection
        const throughPoint = navigationForm.value.through.find(p => p.index === throughIndex);
        if (throughPoint) {
            throughPoint.latitude = location.latitude;
            throughPoint.longitude = location.longitude;
            throughPoint.name = location.name_en || location.name;
            throughPoint.searchQuery = '';
        }
        searchResults.value.through[throughIndex] = [];
        updateThroughMarker(throughIndex, location.latitude, location.longitude);
    } else {
        // Handle from/to selection
        navigationForm.value[type].latitude = location.latitude;
        navigationForm.value[type].longitude = location.longitude;
        navigationForm.value[type].name = location.name_en || location.name;
        navigationForm.value[type].searchQuery = '';
        searchResults.value[type] = [];
        updateMarker(type, location.latitude, location.longitude);
    }

    // Update map view
    if (map.value && location.latitude && location.longitude) {
        map.value.flyTo({
            center: [location.longitude, location.latitude],
            zoom: 15,
            duration: 1000
        });
    }
};

const selectLocationFromMap = async (lat, lng, type, throughIndex = null) => {
    const locationName = await queryLocationName(lat, lng);

    const location = {
        latitude: lat,
        longitude: lng,
        name: locationName
    };

    selectLocation(type, location, throughIndex);
    disableMapSelection();
};

const clearLocation = (type, throughIndex = null) => {
    // Disable map selection if it's active for this location
    if ((throughIndex !== null && mapSelectionMode.value === throughIndex) ||
        (throughIndex === null && mapSelectionMode.value === type)) {
        disableMapSelection();
    }

    if (throughIndex !== null) {
        // Remove through point
        navigationForm.value.through = navigationForm.value.through.filter(p => p.index !== throughIndex);
        delete searchResults.value.through[throughIndex];
        delete isSearching.value.through[throughIndex];
        removeThroughMarker(throughIndex);
        reorderThroughPoints();
    } else {
        // Clear from/to
        navigationForm.value[type] = {
            latitude: null,
            longitude: null,
            name: '',
            searchQuery: ''
        };
        searchResults.value[type] = [];
        removeMarker(type);
    }
};

// Through points management
const addThroughPoint = () => {
    const newIndex = navigationForm.value.through.length > 0
        ? Math.max(...navigationForm.value.through.map(p => p.index)) + 1
        : 0;

    navigationForm.value.through.push({
        index: newIndex,
        latitude: null,
        longitude: null,
        name: '',
        searchQuery: ''
    });

    searchResults.value.through[newIndex] = [];
    isSearching.value.through[newIndex] = false;
};

const reorderThroughPoints = () => {
    navigationForm.value.through.forEach((point, index) => {
        point.index = index;
    });
};

// Map marker management
const updateMarker = (type, lat, lng) => {
    if (markers.value[type]) {
        markers.value[type].remove();
    }

    const color = type === 'from' ? '#22c55e' : '#ef4444'; // Green for from, red for to
    markers.value[type] = new maplibregl.Marker({ color })
        .setLngLat([lng, lat])
        .addTo(map.value);
};

const removeMarker = (type) => {
    if (markers.value[type]) {
        markers.value[type].remove();
        markers.value[type] = null;
    }
};

const updateThroughMarker = (index, lat, lng) => {
    // Remove existing marker if any
    const existingMarkerIndex = markers.value.through.findIndex(m => m.index === index);
    if (existingMarkerIndex !== -1) {
        markers.value.through[existingMarkerIndex].marker.remove();
        markers.value.through.splice(existingMarkerIndex, 1);
    }

    // Add new marker
    const marker = new maplibregl.Marker({ color: '#3b82f6' }) // Blue for through points
        .setLngLat([lng, lat])
        .addTo(map.value);

    markers.value.through.push({ index, marker });
};

const removeThroughMarker = (index) => {
    const markerIndex = markers.value.through.findIndex(m => m.index === index);
    if (markerIndex !== -1) {
        markers.value.through[markerIndex].marker.remove();
        markers.value.through.splice(markerIndex, 1);
    }
};

// Map initialization
const initializeMap = () => {
    map.value = new maplibregl.Map({
        container: mapContainer.value,
        style: {
            version: 8,
            sources: {
                'osm': {
                    type: 'raster',
                    tiles: ['https://tile.openstreetmap.org/{z}/{x}/{y}.png'],
                    tileSize: 256,
                    attribution: '© OpenStreetMap contributors'
                }
            },
            layers: [
                {
                    id: 'osm',
                    type: 'raster',
                    source: 'osm'
                }
            ]
        },
        center: MAP_CONFIG.center,
        zoom: MAP_CONFIG.zoom,
        minZoom: MAP_CONFIG.minZoom,
        maxZoom: MAP_CONFIG.maxZoom
    });

    // Set map bounds to Rwanda
    map.value.setMaxBounds(MAP_CONFIG.bounds);

    // Add click handler for coordinate selection
    map.value.on('click', handleMapClick);

    // Add hover handler for coordinate preview
    map.value.on('mousemove', handleMapHover);
    map.value.on('mouseleave', handleMapMouseLeave);
};

// Handle map clicks for coordinate selection
const handleMapClick = async (e) => {
    const { lng, lat } = e.lngLat;

    if (mapSelectionMode.value !== null) {
        if (typeof mapSelectionMode.value === 'number') {
            // Through point selection
            await selectLocationFromMap(lat, lng, 'through', mapSelectionMode.value);
        } else {
            // From/To selection
            await selectLocationFromMap(lat, lng, mapSelectionMode.value);
        }
    }
};

// Handle map hover for coordinate preview
const handleMapHover = (e) => {
    if (mapSelectionMode.value !== null) {
        const { lng, lat } = e.lngLat;
        hoverCoordinates.value = { latitude: lat, longitude: lng };

        // Debounced location name query
        clearTimeout(hoverCoordinates.value.timeout);
        hoverCoordinates.value.timeout = setTimeout(async () => {
            hoverLocationName.value = await queryLocationName(lat, lng);
        }, 500);
    }
};

const handleMapMouseLeave = () => {
    if (hoverCoordinates.value?.timeout) {
        clearTimeout(hoverCoordinates.value.timeout);
    }
    hoverCoordinates.value = null;
    hoverLocationName.value = '';
};

// Submit navigation request
const submitNavigation = async () => {
    if (!canSubmit.value) return;

    const requestData = {
        from: {
            latitude: navigationForm.value.from.latitude,
            longitude: navigationForm.value.from.longitude
        },
        to: {
            latitude: navigationForm.value.to.latitude,
            longitude: navigationForm.value.to.longitude
        },
        mode: navigationForm.value.mode,
        language: navigationForm.value.language
    };

    // Add through points if any
    if (navigationForm.value.through.length > 0) {
        requestData.throught = navigationForm.value.through
            .filter(point => point.latitude && point.longitude)
            .map(point => ({
                latitude: point.latitude,
                longitude: point.longitude,
                index: point.index
            }));
    }

    try {
        console.log('Navigation request:', requestData);

        const response = await axios.post('/map/navigation/get-navigation', requestData);
        console.log('Navigation response:', response.data);

        // TODO: Handle the navigation response (display route, directions, etc.)

    } catch (error) {
        console.error('Navigation request failed:', error);
        console.error('Error details:', error.response?.data);
    }
};

// Lifecycle hooks
onMounted(() => {
    initializeMap();
});

onUnmounted(() => {
    if (map.value) {
        map.value.remove();
    }
});
</script>

<template>
    <AppLayout title="Navigation">
        <div class="w-full h-screen relative">
        <!-- Map Container -->
        <div ref="mapContainer" class="w-full h-full"></div>

        <!-- Navigation Panel -->
        <div v-if="panelsVisible" class="absolute top-4 left-4 w-96 bg-white rounded-xl border-2 border-black shadow-none z-10 max-h-[calc(100vh-2rem)] overflow-y-auto">
            <div class="p-6">
                <!-- Header -->
                <div class="flex items-center justify-between mb-6">
                    <h2 class="text-xl font-bold text-black">Navigation</h2>
                    <button @click="panelsVisible = false" class="p-2 rounded-lg border border-black hover:bg-gray-100 transition-colors">
                        <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
                        </svg>
                    </button>
                </div>

                <!-- From Location -->
                <div class="mb-6">
                    <label class="block text-sm font-bold text-black mb-2">From *</label>
                    <div v-if="navigationForm.from.name" class="p-3 bg-green-50 border-2 border-green-200 rounded-lg">
                        <div class="flex justify-between items-center">
                            <div>
                                <div class="font-bold text-green-900">{{ navigationForm.from.name }}</div>
                                <div class="text-sm text-green-700">{{ navigationForm.from.latitude }}, {{ navigationForm.from.longitude }}</div>
                            </div>
                            <button @click="clearLocation('from')" class="text-green-600 hover:text-green-800 p-1 rounded-lg hover:bg-green-100 transition-colors">
                                <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
                                </svg>
                            </button>
                        </div>
                    </div>
                    <div v-else class="space-y-3">
                        <div class="relative">
                            <Input
                                v-model="navigationForm.from.searchQuery"
                                @input="performLocationSearch('from', navigationForm.from.searchQuery)"
                                placeholder="Search starting location..."
                                class="w-full rounded-lg border-2 border-black focus:border-black focus:ring-0"
                            />
                            <div v-if="isSearching.from" class="absolute right-3 top-1/2 -translate-y-1/2">
                                <div class="animate-spin rounded-full h-4 w-4 border-2 border-black border-t-transparent"></div>
                            </div>

                            <!-- Search Results -->
                            <div v-if="searchResults.from.length > 0" class="absolute z-20 w-full mt-1 max-h-48 overflow-y-auto bg-white border-2 border-black rounded-lg">
                                <div v-for="result in searchResults.from" :key="`from-${result.type}-${result.id}`"
                                     @click="selectLocation('from', result)"
                                     class="p-3 hover:bg-gray-100 cursor-pointer border-b border-gray-200 last:border-b-0 transition-colors">
                                    <div class="font-bold text-black">{{ result.name_en || result.name }}</div>
                                    <div class="text-sm text-gray-600">{{ result.type }}</div>
                                </div>
                            </div>
                        </div>

                        <!-- Map Selection Button -->
                        <div class="text-center">
                            <span class="text-sm text-gray-500">or</span>
                        </div>
                        <Button
                            @click="enableMapSelection('from')"
                            variant="outline"
                            class="w-full border-2 border-black text-black hover:bg-gray-100"
                            :class="{ 'bg-gray-100': mapSelectionMode === 'from' }"
                        >
                            <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z" />
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 11a3 3 0 11-6 0 3 3 0 016 0z" />
                            </svg>
                            {{ mapSelectionMode === 'from' ? 'Click on map to select' : 'Select from map' }}
                        </Button>
                    </div>
                </div>

                <!-- To Location -->
                <div class="mb-6">
                    <label class="block text-sm font-bold text-black mb-2">To *</label>
                    <div v-if="navigationForm.to.name" class="p-3 bg-red-50 border-2 border-red-200 rounded-lg">
                        <div class="flex justify-between items-center">
                            <div>
                                <div class="font-bold text-red-900">{{ navigationForm.to.name }}</div>
                                <div class="text-sm text-red-700">{{ navigationForm.to.latitude }}, {{ navigationForm.to.longitude }}</div>
                            </div>
                            <button @click="clearLocation('to')" class="text-red-600 hover:text-red-800 p-1 rounded-lg hover:bg-red-100 transition-colors">
                                <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
                                </svg>
                            </button>
                        </div>
                    </div>
                    <div v-else class="space-y-3">
                        <div class="relative">
                            <Input
                                v-model="navigationForm.to.searchQuery"
                                @input="performLocationSearch('to', navigationForm.to.searchQuery)"
                                placeholder="Search destination..."
                                class="w-full rounded-lg border-2 border-black focus:border-black focus:ring-0"
                            />
                            <div v-if="isSearching.to" class="absolute right-3 top-1/2 -translate-y-1/2">
                                <div class="animate-spin rounded-full h-4 w-4 border-2 border-black border-t-transparent"></div>
                            </div>

                            <!-- Search Results -->
                            <div v-if="searchResults.to.length > 0" class="absolute z-20 w-full mt-1 max-h-48 overflow-y-auto bg-white border-2 border-black rounded-lg">
                                <div v-for="result in searchResults.to" :key="`to-${result.type}-${result.id}`"
                                     @click="selectLocation('to', result)"
                                     class="p-3 hover:bg-gray-100 cursor-pointer border-b border-gray-200 last:border-b-0 transition-colors">
                                    <div class="font-bold text-black">{{ result.name_en || result.name }}</div>
                                    <div class="text-sm text-gray-600">{{ result.type }}</div>
                                </div>
                            </div>
                        </div>

                        <!-- Map Selection Button -->
                        <div class="text-center">
                            <span class="text-sm text-gray-500">or</span>
                        </div>
                        <Button
                            @click="enableMapSelection('to')"
                            variant="outline"
                            class="w-full border-2 border-black text-black hover:bg-gray-100"
                            :class="{ 'bg-gray-100': mapSelectionMode === 'to' }"
                        >
                            <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z" />
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 11a3 3 0 11-6 0 3 3 0 016 0z" />
                            </svg>
                            {{ mapSelectionMode === 'to' ? 'Click on map to select' : 'Select from map' }}
                        </Button>
                    </div>
                </div>

                <!-- Through Points -->
                <div class="mb-6">
                    <div class="flex items-center justify-between mb-3">
                        <label class="block text-sm font-bold text-black">Through Points</label>
                        <Button @click="addThroughPoint" variant="outline" size="sm" class="border-black text-black hover:bg-gray-100">
                            <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4v16m8-8H4" />
                            </svg>
                            Add Stop
                        </Button>
                    </div>

                    <div v-if="throughPointsOrdered.length === 0" class="text-sm text-gray-500 italic">
                        No stops added. Click "Add Stop" to add waypoints.
                    </div>

                    <div v-for="(point, index) in throughPointsOrdered" :key="point.index" class="mb-3">
                        <div class="flex items-center gap-2 mb-2">
                            <span class="text-xs font-bold text-black bg-gray-200 rounded-full w-6 h-6 flex items-center justify-center">{{ index + 1 }}</span>
                            <span class="text-sm font-medium text-black">Stop {{ index + 1 }}</span>
                        </div>

                        <div v-if="point.name" class="p-3 bg-blue-50 border-2 border-blue-200 rounded-lg">
                            <div class="flex justify-between items-center">
                                <div>
                                    <div class="font-bold text-blue-900">{{ point.name }}</div>
                                    <div class="text-sm text-blue-700">{{ point.latitude }}, {{ point.longitude }}</div>
                                </div>
                                <button @click="clearLocation('through', point.index)" class="text-blue-600 hover:text-blue-800 p-1 rounded-lg hover:bg-blue-100 transition-colors">
                                    <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
                                    </svg>
                                </button>
                            </div>
                        </div>
                        <div v-else class="space-y-3">
                            <div class="relative">
                                <Input
                                    v-model="point.searchQuery"
                                    @input="performLocationSearch('through', point.searchQuery, point.index)"
                                    :placeholder="`Search stop ${index + 1}...`"
                                    class="w-full rounded-lg border-2 border-black focus:border-black focus:ring-0"
                                />
                                <div v-if="isSearching.through[point.index]" class="absolute right-3 top-1/2 -translate-y-1/2">
                                    <div class="animate-spin rounded-full h-4 w-4 border-2 border-black border-t-transparent"></div>
                                </div>

                                <!-- Search Results -->
                                <div v-if="searchResults.through[point.index]?.length > 0" class="absolute z-20 w-full mt-1 max-h-48 overflow-y-auto bg-white border-2 border-black rounded-lg">
                                    <div v-for="result in searchResults.through[point.index]" :key="`through-${point.index}-${result.type}-${result.id}`"
                                         @click="selectLocation('through', result, point.index)"
                                         class="p-3 hover:bg-gray-100 cursor-pointer border-b border-gray-200 last:border-b-0 transition-colors">
                                        <div class="font-bold text-black">{{ result.name_en || result.name }}</div>
                                        <div class="text-sm text-gray-600">{{ result.type }}</div>
                                    </div>
                                </div>
                            </div>

                            <!-- Map Selection Button -->
                            <div class="text-center">
                                <span class="text-sm text-gray-500">or</span>
                            </div>
                            <Button
                                @click="enableMapSelection('through', point.index)"
                                variant="outline"
                                class="w-full border-2 border-black text-black hover:bg-gray-100"
                                :class="{ 'bg-gray-100': mapSelectionMode === point.index }"
                            >
                                <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z" />
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 11a3 3 0 11-6 0 3 3 0 016 0z" />
                                </svg>
                                {{ mapSelectionMode === point.index ? 'Click on map to select' : 'Select from map' }}
                            </Button>
                        </div>
                    </div>
                </div>

                <!-- Mode Selection -->
                <div class="mb-6">
                    <label class="block text-sm font-bold text-black mb-2">Travel Mode *</label>
                    <Select v-model="navigationForm.mode">
                        <SelectTrigger class="w-full rounded-lg border-2 border-black focus:border-black focus:ring-0">
                            <SelectValue placeholder="Select travel mode" />
                        </SelectTrigger>
                        <SelectContent class="border-2 border-black rounded-lg">
                            <SelectItem value="car">
                                <div class="flex items-center gap-2">
                                    <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 17a2 2 0 11-4 0 2 2 0 014 0zM19 17a2 2 0 11-4 0 2 2 0 014 0z" />
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 6h3l2 7H8l2-7h3z" />
                                    </svg>
                                    Car
                                </div>
                            </SelectItem>
                            <SelectItem value="bike">
                                <div class="flex items-center gap-2">
                                    <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <circle cx="18.5" cy="17.5" r="3.5"/>
                                        <circle cx="5.5" cy="17.5" r="3.5"/>
                                        <circle cx="15" cy="5" r="1"/>
                                        <path d="M12 17.5V14l-3-3 4-3 2 3h2"/>
                                    </svg>
                                    Bike
                                </div>
                            </SelectItem>
                            <SelectItem value="walk">
                                <div class="flex items-center gap-2">
                                    <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
                                    </svg>
                                    Walk
                                </div>
                            </SelectItem>
                        </SelectContent>
                    </Select>
                </div>

                <!-- Language Selection -->
                <div class="mb-6">
                    <label class="block text-sm font-bold text-black mb-2">Language *</label>
                    <Select v-model="navigationForm.language">
                        <SelectTrigger class="w-full rounded-lg border-2 border-black focus:border-black focus:ring-0">
                            <SelectValue placeholder="Select language" />
                        </SelectTrigger>
                        <SelectContent class="border-2 border-black rounded-lg">
                            <SelectItem value="en">English</SelectItem>
                            <SelectItem value="fr">Français</SelectItem>
                            <SelectItem value="rw">Kinyarwanda</SelectItem>
                        </SelectContent>
                    </Select>
                </div>

                <!-- Submit Button -->
                <Button
                    @click="submitNavigation"
                    :disabled="!canSubmit"
                    class="w-full rounded-lg border-2 border-black bg-black text-white hover:bg-gray-800 disabled:opacity-50 disabled:cursor-not-allowed"
                >
                    Get Directions
                </Button>
            </div>
        </div>

        <!-- Toggle Panel Button -->
        <button v-if="!panelsVisible" @click="panelsVisible = true"
                class="absolute top-4 left-4 p-3 bg-white rounded-lg border-2 border-black hover:bg-gray-100 transition-colors z-10">
            <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7" />
            </svg>
        </button>

        <!-- Map Selection Mode Overlay -->
        <div v-if="mapSelectionMode !== null" class="absolute top-4 right-4 bg-white rounded-lg border-2 border-black p-4 z-10 max-w-sm">
            <div class="flex items-center justify-between mb-3">
                <h3 class="font-bold text-black">
                    {{ typeof mapSelectionMode === 'number' ? `Select Stop ${mapSelectionMode + 1}` : `Select ${mapSelectionMode} location` }}
                </h3>
                <button @click="disableMapSelection" class="text-black hover:text-gray-600 p-1 rounded-lg hover:bg-gray-100 transition-colors">
                    <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
                    </svg>
                </button>
            </div>
            <p class="text-sm text-gray-600 mb-3">Click anywhere on the map to select a location</p>

            <!-- Hover Coordinates Display -->
            <div v-if="hoverCoordinates" class="bg-gray-50 border border-gray-200 rounded-lg p-3">
                <div class="text-xs text-gray-500 mb-1">Hover Location:</div>
                <div class="text-sm font-mono text-black">
                    {{ hoverCoordinates.latitude.toFixed(6) }}, {{ hoverCoordinates.longitude.toFixed(6) }}
                </div>
                <div v-if="hoverLocationName" class="text-sm text-gray-700 mt-1">
                    {{ hoverLocationName }}
                </div>
                <div v-else-if="isLoadingHoverLocation" class="text-sm text-gray-500 mt-1">
                    Loading location name...
                </div>
            </div>

            <Button @click="disableMapSelection" variant="outline" class="w-full mt-3 border-black text-black hover:bg-gray-100">
                Cancel Selection
            </Button>
        </div>
        </div>
    </AppLayout>
</template>
